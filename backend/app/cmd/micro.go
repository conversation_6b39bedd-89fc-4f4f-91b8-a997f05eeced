package cmd

import (
	"context"
	"fmt"
	"time"

	"github.com/sayeworldevelopment/wp-core/pkg/config"
	"github.com/sayeworldevelopment/wp-core/pkg/database"
	"github.com/sayeworldevelopment/wp-core/pkg/entities"
	"github.com/sayeworldevelopment/wp-core/pkg/log"
	"github.com/sayeworldevelopment/wp-core/pkg/server"
	"github.com/sayeworldevelopment/wp-core/pkg/wrapper"
	"go.mau.fi/whatsmeow/types"
)

func StartApp() {
	config := config.InitConfig()
	database.InitDB(config.Database)
	wrapper.Init(config.Database)

	SetDevice()

	// Register immediate websocket error handler
	entities.WebsocketErrorHandler = HandleWebsocketErrorImmediate

	// Start cron job to run SetDevice every 3 minutes
	//go StartSetDeviceCron()

	// Start cron job to monitor websocket errors every 1 minute (backup for any missed immediate handling)
	go StartWebsocketErrorMonitor()

	fmt.Println("Starting app..., port:", config.App.Port)
	server.LaunchHttpServer(config.App, config.Allows)
}

func SetDevice() {
	fmt.Println("init devices")
	type Device struct {
		JID   string `gorm:"column:jid"`
		RegId string `gorm:"column:registration_id"`
	}
	var devices []Device
	var ctx = context.Background()

	db := database.DBClient()
	w := wrapper.WpClient()

	db.Select("jid,registration_id").Table("whatsmeow_device").Find(&devices)
	for _, v := range devices {
		// Use wrapper's CheckDevice instead of nat's
		client, isConnected := w.CheckDevice(ctx, v.JID, v.RegId)
		if !isConnected {
			log.CreateLog(&entities.Log{
				Title:   "Check Device Failed for Restart",
				Message: "Check Device Failed, check device err",
				Entity:  "user",
				Type:    "error",
				Ip:      "",
			})
			continue
		}

		var session entities.Session
		if err := db.Model(&entities.Session{}).
			Where("jid = ?", v.JID).
			First(&session).Error; err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Check Device Failed for Restart",
				Message: "Check Device Failed, session not found",
				Entity:  "user",
				Type:    "error",
				Ip:      "",
			})

			continue
		}

		// Get all active subscriptions for this session
		var subscriptions []entities.SessionSubscription
		err := db.Where("session_id = ? AND active = ?", session.ID, true).Find(&subscriptions).Error
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Check Device Failed for Restart",
				Message: "Check Device Failed, get subscriptions err:" + err.Error(),
				Entity:  "user",
				Type:    "error",
				Ip:      "",
			})

			continue
		}

		// Subscribe to each phone number
		for _, sub := range subscriptions {
			// Remove existing event handler if it exists
			if sub.EventHandlerId != 0 {
				success := client.RemoveEventHandler(sub.EventHandlerId)
				if success {
					log.CreateLog(&entities.Log{
						Title:   "Event Handler Removed",
						Message: fmt.Sprintf("Successfully removed event handler ID: %d for phone: %s", sub.EventHandlerId, sub.Phone),
						Entity:  "system",
						Type:    "info",
						Ip:      "",
					})
				} else {
					log.CreateLog(&entities.Log{
						Title:   "Event Handler Remove Failed",
						Message: fmt.Sprintf("Failed to remove event handler ID: %d for phone: %s", sub.EventHandlerId, sub.Phone),
						Entity:  "system",
						Type:    "warning",
						Ip:      "",
					})
				}
			}

			// Add new event handler
			newEventHandlerId := client.AddEventHandler(wrapper.EventHandler)

			// Update the EventHandlerId in the database
			err = db.Model(&entities.SessionSubscription{}).
				Where("id = ?", sub.ID).
				Update("event_handler_id", newEventHandlerId).Error
			if err != nil {
				log.CreateLog(&entities.Log{
					Title:   "Update Event Handler ID Failed",
					Message: "Failed to update event handler ID in database: " + err.Error(),
					Entity:  "system",
					Type:    "error",
					Ip:      "",
				})
				continue
			}

			subJid := sub.Phone + "@s.whatsapp.net"
			parsedJID, _ := types.ParseJID(subJid)
			client.SendPresence(types.PresenceAvailable)

			err = client.SubscribePresence(parsedJID)
			if err != nil {
				log.CreateLog(&entities.Log{
					Title:   "Check Device Failed for Restart",
					Message: "Check Device Failed, subscribe presence err:" + err.Error(),
					Entity:  "user",
					Type:    "error",
					Ip:      "",
				})
				continue
			}
			//client.SendPresence(types.PresenceAvailable)

			log.CreateLog(&entities.Log{
				Title:   "Subscription Renewed",
				Message: fmt.Sprintf("Successfully renewed subscription for phone: %s with new event handler ID: %d", sub.Phone, newEventHandlerId),
				Entity:  "system",
				Type:    "info",
				Ip:      "",
			})
		}

	}
}

// StartSetDeviceCron starts a cron job that runs SetDevice every 3 minutes
func StartSetDeviceCron() {
	fmt.Println("Starting SetDevice cron job - will run every 3 minutes...")

	// Create a ticker that fires every 3 minutes
	ticker := time.NewTicker(3 * time.Minute)
	defer ticker.Stop()

	// Log the cron job start
	log.CreateLog(&entities.Log{
		Title:   "SetDevice Cron Started",
		Message: "SetDevice cron job started - will run every 3 minutes",
		Entity:  "system",
		Type:    "info",
		Ip:      "system",
	})

	// Run the cron job
	for range ticker.C {
		fmt.Println("Running scheduled SetDevice...")

		// Log the scheduled run
		log.CreateLog(&entities.Log{
			Title:   "SetDevice Scheduled Run",
			Message: "Running SetDevice as scheduled (every 3 minutes)",
			Entity:  "system",
			Type:    "info",
			Ip:      "system",
		})

		// Run SetDevice
		SetDevice()
	}
}

// StartWebsocketErrorMonitor starts a cron job that monitors websocket errors every 1 minute
func StartWebsocketErrorMonitor() {
	fmt.Println("Starting WebsocketError monitor - will run every 1 minute...")

	// Create a ticker that fires every 1 minute
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	// Log the cron job start
	log.CreateLog(&entities.Log{
		Title:   "WebsocketError Monitor Started",
		Message: "WebsocketError monitor started - will run every 1 minute",
		Entity:  "system",
		Type:    "info",
		Ip:      "system",
	})

	// Run the cron job
	for range ticker.C {
		fmt.Println("Running websocket error check...")

		// Log the scheduled run
		log.CreateLog(&entities.Log{
			Title:   "WebsocketError Monitor Run",
			Message: "Running websocket error check (every 1 minute)",
			Entity:  "system",
			Type:    "info",
			Ip:      "system",
		})

		// Check for websocket errors and reconnect if needed
		CheckWebsocketErrors()
	}
}

// CheckWebsocketErrors checks for unprocessed websocket errors and reconnects clients
func CheckWebsocketErrors() {
	var ctx = context.Background()
	db := database.DBClient()
	w := wrapper.WpClient()

	// Get all unprocessed websocket errors
	var errors []entities.WebsocketError
	err := db.Where("processed = ?", false).Find(&errors).Error
	if err != nil {
		log.CreateLog(&entities.Log{
			Title:   "WebsocketError Check Failed",
			Message: "Failed to get websocket errors: " + err.Error(),
			Entity:  "system",
			Type:    "error",
			Ip:      "system",
		})
		return
	}

	if len(errors) == 0 {
		return // No errors to process
	}

	log.CreateLog(&entities.Log{
		Title:   "WebsocketError Processing",
		Message: fmt.Sprintf("Found %d unprocessed websocket errors", len(errors)),
		Entity:  "system",
		Type:    "info",
		Ip:      "system",
	})

	for _, wsError := range errors {
		// Find the session for this JID
		var session entities.Session
		if err := db.Model(&entities.Session{}).
			Where("jid = ?", wsError.JID).
			First(&session).Error; err != nil {
			log.CreateLog(&entities.Log{
				Title:   "WebsocketError Processing Failed",
				Message: "Session not found for JID: " + wsError.JID,
				Entity:  "system",
				Type:    "error",
				Ip:      "system",
			})

			// Mark as processed even if session not found
			db.Model(&wsError).Update("processed", true)
			continue
		}

		log.CreateLog(&entities.Log{
			Title:   "WebsocketError Reconnecting",
			Message: fmt.Sprintf("Reconnecting client for JID: %s due to error: %s", wsError.JID, wsError.ErrorMessage),
			Entity:  "system",
			Type:    "info",
			Ip:      "system",
		})

		// Use CheckDevice to reconnect (this will close existing client and create new one)
		client, isConnected := w.CheckDevice(ctx, wsError.JID, wsError.RegID)
		if !isConnected {
			log.CreateLog(&entities.Log{
				Title:   "WebsocketError Reconnect Failed",
				Message: "Failed to reconnect client for JID: " + wsError.JID,
				Entity:  "system",
				Type:    "error",
				Ip:      "system",
			})

			// Mark as processed even if reconnection failed
			db.Model(&wsError).Update("processed", true)
			continue
		}

		// Get all active subscriptions for this session and re-subscribe
		var subscriptions []entities.SessionSubscription
		err := db.Where("session_id = ? AND active = ?", session.ID, true).Find(&subscriptions).Error
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "WebsocketError Reconnect Failed",
				Message: "Failed to get subscriptions for session: " + err.Error(),
				Entity:  "system",
				Type:    "error",
				Ip:      "system",
			})

			// Mark as processed
			db.Model(&wsError).Update("processed", true)
			continue
		}

		// Re-subscribe to each phone number
		for _, sub := range subscriptions {
			// Remove existing event handler if it exists
			if sub.EventHandlerId != 0 {
				client.RemoveEventHandler(sub.EventHandlerId)
			}

			// Add new event handler
			newEventHandlerId := client.AddEventHandler(wrapper.EventHandler)

			// Update the EventHandlerId in the database
			db.Model(&entities.SessionSubscription{}).
				Where("id = ?", sub.ID).
				Update("event_handler_id", newEventHandlerId)

			subJid := sub.Phone + "@s.whatsapp.net"
			parsedJID, _ := types.ParseJID(subJid)
			client.SendPresence(types.PresenceAvailable)

			err = client.SubscribePresence(parsedJID)
			if err != nil {
				log.CreateLog(&entities.Log{
					Title:   "WebsocketError Re-subscribe Failed",
					Message: "Failed to re-subscribe to presence for phone: " + sub.Phone + ", error: " + err.Error(),
					Entity:  "system",
					Type:    "error",
					Ip:      "system",
				})
				continue
			}
			client.SendPresence(types.PresenceAvailable)

			log.CreateLog(&entities.Log{
				Title:   "WebsocketError Re-subscribe Success",
				Message: fmt.Sprintf("Successfully re-subscribed to phone: %s with new event handler ID: %d", sub.Phone, newEventHandlerId),
				Entity:  "system",
				Type:    "info",
				Ip:      "system",
			})
		}

		// Mark the error as processed
		err = db.Model(&wsError).Update("processed", true).Error
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "WebsocketError Update Failed",
				Message: "Failed to mark websocket error as processed: " + err.Error(),
				Entity:  "system",
				Type:    "error",
				Ip:      "system",
			})
		} else {
			log.CreateLog(&entities.Log{
				Title:   "WebsocketError Processed",
				Message: fmt.Sprintf("Successfully processed websocket error for JID: %s", wsError.JID),
				Entity:  "system",
				Type:    "info",
				Ip:      "system",
			})
		}
	}
}

// HandleWebsocketErrorImmediate handles websocket errors immediately when they are created
func HandleWebsocketErrorImmediate(wsError entities.WebsocketError) {
	var ctx = context.Background()
	db := database.DBClient()
	w := wrapper.WpClient()

	log.CreateLog(&entities.Log{
		Title:   "Immediate WebsocketError Processing",
		Message: fmt.Sprintf("Processing websocket error immediately for JID: %s, Error: %s", wsError.JID, wsError.ErrorMessage),
		Entity:  "system",
		Type:    "info",
		Ip:      "system",
	})

	// Find the session for this JID
	var session entities.Session
	if err := db.Model(&entities.Session{}).
		Where("jid = ?", wsError.JID).
		First(&session).Error; err != nil {
		log.CreateLog(&entities.Log{
			Title:   "Immediate WebsocketError Processing Failed",
			Message: "Session not found for JID: " + wsError.JID,
			Entity:  "system",
			Type:    "error",
			Ip:      "system",
		})

		// Mark as processed even if session not found
		db.Model(&wsError).Update("processed", true)
		return
	}

	log.CreateLog(&entities.Log{
		Title:   "Immediate WebsocketError Reconnecting",
		Message: fmt.Sprintf("Immediately reconnecting client for JID: %s due to error: %s", wsError.JID, wsError.ErrorMessage),
		Entity:  "system",
		Type:    "info",
		Ip:      "system",
	})

	// Use CheckDevice to reconnect (this will close existing client and create new one)
	client, isConnected := w.CheckDevice(ctx, wsError.JID, wsError.RegID)
	if !isConnected {
		log.CreateLog(&entities.Log{
			Title:   "Immediate WebsocketError Reconnect Failed",
			Message: "Failed to immediately reconnect client for JID: " + wsError.JID,
			Entity:  "system",
			Type:    "error",
			Ip:      "system",
		})

		// Don't mark as processed if immediate reconnection failed - let cron job retry
		return
	}

	// Get all active subscriptions for this session and re-subscribe
	var subscriptions []entities.SessionSubscription
	err := db.Where("session_id = ? AND active = ?", session.ID, true).Find(&subscriptions).Error
	if err != nil {
		log.CreateLog(&entities.Log{
			Title:   "Immediate WebsocketError Reconnect Failed",
			Message: "Failed to get subscriptions for session: " + err.Error(),
			Entity:  "system",
			Type:    "error",
			Ip:      "system",
		})

		// Don't mark as processed if subscription restoration failed
		return
	}

	// Re-subscribe to each phone number
	for _, sub := range subscriptions {
		// Remove existing event handler if it exists
		if sub.EventHandlerId != 0 {
			client.RemoveEventHandler(sub.EventHandlerId)
		}

		// Add new event handler
		newEventHandlerId := client.AddEventHandler(wrapper.EventHandler)

		// Update the EventHandlerId in the database
		db.Model(&entities.SessionSubscription{}).
			Where("id = ?", sub.ID).
			Update("event_handler_id", newEventHandlerId)

		subJid := sub.Phone + "@s.whatsapp.net"
		parsedJID, _ := types.ParseJID(subJid)
		client.SendPresence(types.PresenceAvailable)

		err = client.SubscribePresence(parsedJID)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Immediate WebsocketError Re-subscribe Failed",
				Message: "Failed to immediately re-subscribe to presence for phone: " + sub.Phone + ", error: " + err.Error(),
				Entity:  "system",
				Type:    "error",
				Ip:      "system",
			})
			continue
		}
		client.SendPresence(types.PresenceAvailable)

		log.CreateLog(&entities.Log{
			Title:   "Immediate WebsocketError Re-subscribe Success",
			Message: fmt.Sprintf("Successfully immediately re-subscribed to phone: %s with new event handler ID: %d", sub.Phone, newEventHandlerId),
			Entity:  "system",
			Type:    "info",
			Ip:      "system",
		})
	}

	// Mark the error as processed
	err = db.Model(&wsError).Update("processed", true).Error
	if err != nil {
		log.CreateLog(&entities.Log{
			Title:   "Immediate WebsocketError Update Failed",
			Message: "Failed to mark websocket error as processed: " + err.Error(),
			Entity:  "system",
			Type:    "error",
			Ip:      "system",
		})
	} else {
		log.CreateLog(&entities.Log{
			Title:   "Immediate WebsocketError Processed",
			Message: fmt.Sprintf("Successfully processed websocket error immediately for JID: %s", wsError.JID),
			Entity:  "system",
			Type:    "info",
			Ip:      "system",
		})
	}
}
