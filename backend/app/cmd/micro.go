package cmd

import (
	"context"
	"fmt"
	"time"

	"github.com/sayeworldevelopment/wp-core/pkg/config"
	"github.com/sayeworldevelopment/wp-core/pkg/database"
	"github.com/sayeworldevelopment/wp-core/pkg/entities"
	"github.com/sayeworldevelopment/wp-core/pkg/log"
	"github.com/sayeworldevelopment/wp-core/pkg/server"
	"github.com/sayeworldevelopment/wp-core/pkg/wrapper"
	"go.mau.fi/whatsmeow/types"
)

func StartApp() {
	config := config.InitConfig()
	database.InitDB(config.Database)
	wrapper.Init(config.Database)

	SetDevice()

	// Note: We'll monitor whatsmeow_websocket_errors table directly via cron job

	// Start cron job to run SetDevice every 3 minutes
	//go StartSetDeviceCron()

	// Start cron job to monitor websocket errors every 1 minute (backup for any missed immediate handling)
	go StartWebsocketErrorMonitor()

	// Start cron job to monitor websocket health every 10 seconds
	go StartWebsocketHealthMonitor()

	fmt.Println("Starting app..., port:", config.App.Port)
	server.LaunchHttpServer(config.App, config.Allows)
}

func SetDevice() {
	fmt.Println("init devices")
	type Device struct {
		JID   string `gorm:"column:jid"`
		RegId string `gorm:"column:registration_id"`
	}
	var devices []Device
	var ctx = context.Background()

	db := database.DBClient()
	w := wrapper.WpClient()

	db.Select("jid,registration_id").Table("whatsmeow_device").Find(&devices)
	for _, v := range devices {
		// Use wrapper's CheckDevice instead of nat's
		client, isConnected := w.CheckDevice(ctx, v.JID, v.RegId)
		if !isConnected {
			log.CreateLog(&entities.Log{
				Title:   "Check Device Failed for Restart",
				Message: "Check Device Failed, check device err",
				Entity:  "user",
				Type:    "error",
				Ip:      "",
			})
			continue
		}

		var session entities.Session
		if err := db.Model(&entities.Session{}).
			Where("jid = ?", v.JID).
			First(&session).Error; err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Check Device Failed for Restart",
				Message: "Check Device Failed, session not found",
				Entity:  "user",
				Type:    "error",
				Ip:      "",
			})

			continue
		}

		// Get all active subscriptions for this session
		var subscriptions []entities.SessionSubscription
		err := db.Where("session_id = ? AND active = ?", session.ID, true).Find(&subscriptions).Error
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Check Device Failed for Restart",
				Message: "Check Device Failed, get subscriptions err:" + err.Error(),
				Entity:  "user",
				Type:    "error",
				Ip:      "",
			})

			continue
		}

		// Subscribe to each phone number
		for _, sub := range subscriptions {
			// Remove existing event handler if it exists
			if sub.EventHandlerId != 0 {
				success := client.RemoveEventHandler(sub.EventHandlerId)
				if success {
					log.CreateLog(&entities.Log{
						Title:   "Event Handler Removed",
						Message: fmt.Sprintf("Successfully removed event handler ID: %d for phone: %s", sub.EventHandlerId, sub.Phone),
						Entity:  "system",
						Type:    "info",
						Ip:      "",
					})
				} else {
					log.CreateLog(&entities.Log{
						Title:   "Event Handler Remove Failed",
						Message: fmt.Sprintf("Failed to remove event handler ID: %d for phone: %s", sub.EventHandlerId, sub.Phone),
						Entity:  "system",
						Type:    "warning",
						Ip:      "",
					})
				}
			}

			// Add new event handler
			newEventHandlerId := client.AddEventHandler(wrapper.EventHandler)

			// Update the EventHandlerId in the database
			err = db.Model(&entities.SessionSubscription{}).
				Where("id = ?", sub.ID).
				Update("event_handler_id", newEventHandlerId).Error
			if err != nil {
				log.CreateLog(&entities.Log{
					Title:   "Update Event Handler ID Failed",
					Message: "Failed to update event handler ID in database: " + err.Error(),
					Entity:  "system",
					Type:    "error",
					Ip:      "",
				})
				continue
			}

			subJid := sub.Phone + "@s.whatsapp.net"
			parsedJID, _ := types.ParseJID(subJid)
			err = client.SubscribePresenceOptimized(parsedJID)
			if err != nil {
				log.CreateLog(&entities.Log{
					Title:   "Check Device Failed for Restart",
					Message: "Check Device Failed, subscribe presence err:" + err.Error(),
					Entity:  "user",
					Type:    "error",
					Ip:      "",
				})
				continue
			}
			//client.SendPresence(types.PresenceAvailable)

			log.CreateLog(&entities.Log{
				Title:   "Subscription Renewed",
				Message: fmt.Sprintf("Successfully renewed subscription for phone: %s with new event handler ID: %d", sub.Phone, newEventHandlerId),
				Entity:  "system",
				Type:    "info",
				Ip:      "",
			})
		}

	}
}

// StartSetDeviceCron starts a cron job that runs SetDevice every 3 minutes
func StartSetDeviceCron() {
	fmt.Println("Starting SetDevice cron job - will run every 3 minutes...")

	// Create a ticker that fires every 3 minutes
	ticker := time.NewTicker(3 * time.Minute)
	defer ticker.Stop()

	// Log the cron job start
	log.CreateLog(&entities.Log{
		Title:   "SetDevice Cron Started",
		Message: "SetDevice cron job started - will run every 3 minutes",
		Entity:  "system",
		Type:    "info",
		Ip:      "system",
	})

	// Run the cron job
	for range ticker.C {
		fmt.Println("Running scheduled SetDevice...")

		// Log the scheduled run
		log.CreateLog(&entities.Log{
			Title:   "SetDevice Scheduled Run",
			Message: "Running SetDevice as scheduled (every 3 minutes)",
			Entity:  "system",
			Type:    "info",
			Ip:      "system",
		})

		// Run SetDevice
		SetDevice()
	}
}

// StartWebsocketErrorMonitor starts a cron job that monitors websocket errors every 1 minute
func StartWebsocketErrorMonitor() {
	fmt.Println("Starting WebsocketError monitor - will run every 1 minute...")

	// Create a ticker that fires every 1 minute
	ticker := time.NewTicker(25 * time.Second)
	defer ticker.Stop()

	// Log the cron job start
	log.CreateLog(&entities.Log{
		Title:   "WebsocketError Monitor Started",
		Message: "WebsocketError monitor started - will run every 1 minute",
		Entity:  "system",
		Type:    "info",
		Ip:      "system",
	})

	// Run the cron job
	for range ticker.C {
		fmt.Println("Running websocket error check...")

		// Log the scheduled run
		log.CreateLog(&entities.Log{
			Title:   "WebsocketError Monitor Run",
			Message: "Running websocket error check (every 1 minute)",
			Entity:  "system",
			Type:    "info",
			Ip:      "system",
		})

		// Check for websocket errors and reconnect if needed
		CheckWebsocketErrors()
	}
}

// CheckWebsocketErrors checks for unprocessed websocket errors and reconnects clients
func CheckWebsocketErrors() {
	var ctx = context.Background()
	db := database.DBClient()
	w := wrapper.WpClient()

	// Get all unprocessed websocket errors from whatsmeow table
	// Only process errors that are older than 60 seconds to avoid rapid reconnections
	var errors []entities.WhatsmeowWebsocketError
	err := db.Table("whatsmeow_websocket_errors").
		Where("(processed = ? OR processed IS NULL) AND timestamp < ?", false, time.Now().Unix()-25).
		Find(&errors).Error
	if err != nil {
		log.CreateLog(&entities.Log{
			Title:   "WebsocketError Check Failed",
			Message: "Failed to get websocket errors: " + err.Error(),
			Entity:  "system",
			Type:    "error",
			Ip:      "system",
		})
		return
	}

	if len(errors) == 0 {
		return // No errors to process
	}

	log.CreateLog(&entities.Log{
		Title:   "WebsocketError Processing",
		Message: fmt.Sprintf("Found %d unprocessed websocket errors", len(errors)),
		Entity:  "system",
		Type:    "info",
		Ip:      "system",
	})

	for _, wsError := range errors {
		// Find the session for this JID
		var session entities.Session
		if err := db.Model(&entities.Session{}).
			Where("jid = ?", wsError.ClientJID).
			First(&session).Error; err != nil {
			log.CreateLog(&entities.Log{
				Title:   "WebsocketError Processing Failed",
				Message: "Session not found for JID: " + wsError.ClientJID,
				Entity:  "system",
				Type:    "error",
				Ip:      "system",
			})

			// Mark as processed even if session not found
			db.Table("whatsmeow_websocket_errors").
				Where("id = ?", wsError.ID).
				Update("processed", true)
			continue
		}

		log.CreateLog(&entities.Log{
			Title:   "WebsocketError Reconnecting",
			Message: fmt.Sprintf("Reconnecting client for JID: %s due to error: %s", wsError.ClientJID, wsError.Error),
			Entity:  "system",
			Type:    "info",
			Ip:      "system",
		})

		// Use CheckDevice to reconnect (this will close existing client and create new one)
		client, isConnected := w.CheckDevice(ctx, wsError.ClientJID, session.RegID)
		if !isConnected {
			log.CreateLog(&entities.Log{
				Title:   "WebsocketError Reconnect Failed",
				Message: "Failed to reconnect client for JID: " + wsError.ClientJID + ", will retry in next cycle",
				Entity:  "system",
				Type:    "error",
				Ip:      "system",
			})

			// Don't mark as processed if reconnection failed - let it retry
			continue
		}

		// Get all active subscriptions for this session and re-subscribe
		var subscriptions []entities.SessionSubscription
		err := db.Where("session_id = ? AND active = ?", session.ID, true).Find(&subscriptions).Error
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "WebsocketError Reconnect Failed",
				Message: "Failed to get subscriptions for session: " + err.Error() + ", will retry in next cycle",
				Entity:  "system",
				Type:    "error",
				Ip:      "system",
			})

			// Don't mark as processed if subscription fetch failed
			continue
		}

		// Re-subscribe to each phone number with retry logic
		subscriptionSuccess := true
		for _, sub := range subscriptions {
			// Remove existing event handler if it exists
			if sub.EventHandlerId != 0 {
				client.RemoveEventHandler(sub.EventHandlerId)
			}

			// Add new event handler
			newEventHandlerId := client.AddEventHandler(wrapper.EventHandler)

			// Update the EventHandlerId in the database
			db.Model(&entities.SessionSubscription{}).
				Where("id = ?", sub.ID).
				Update("event_handler_id", newEventHandlerId)

			subJid := sub.Phone + "@s.whatsapp.net"
			parsedJID, _ := types.ParseJID(subJid)

			// Use optimized presence subscription
			subscribeErr := client.SubscribePresenceOptimized(parsedJID)
			if subscribeErr != nil {
				log.CreateLog(&entities.Log{
					Title:   "WebsocketError Re-subscribe Failed",
					Message: "Failed to re-subscribe to presence for phone: " + sub.Phone + ", error: " + subscribeErr.Error(),
					Entity:  "system",
					Type:    "error",
					Ip:      "system",
				})
				subscriptionSuccess = false
				continue
			}

			log.CreateLog(&entities.Log{
				Title:   "WebsocketError Re-subscribe Success",
				Message: fmt.Sprintf("Successfully re-subscribed to phone: %s with new event handler ID: %d", sub.Phone, newEventHandlerId),
				Entity:  "system",
				Type:    "info",
				Ip:      "system",
			})
		}

		// Only mark as processed if all subscriptions were successful
		if !subscriptionSuccess {
			log.CreateLog(&entities.Log{
				Title:   "WebsocketError Partial Failure",
				Message: fmt.Sprintf("Some subscriptions failed for JID: %s, will retry in next cycle", wsError.ClientJID),
				Entity:  "system",
				Type:    "warning",
				Ip:      "system",
			})
			continue // Don't mark as processed, let it retry
		}

		// Validate presence subscriptions are working
		log.CreateLog(&entities.Log{
			Title:   "WebsocketError Validating Presence",
			Message: fmt.Sprintf("Validating presence subscriptions for JID: %s", wsError.ClientJID),
			Entity:  "system",
			Type:    "info",
			Ip:      "system",
		})

		// Validate websocket health using whatsmeow's built-in function
		isHealthy, err := client.CheckWebSocketHealth()
		if err != nil || !isHealthy {
			log.CreateLog(&entities.Log{
				Title:   "WebsocketError Health Check Failed",
				Message: fmt.Sprintf("WebSocket health check failed for JID: %s, error: %v, will retry in next cycle", wsError.ClientJID, err),
				Entity:  "system",
				Type:    "warning",
				Ip:      "system",
			})
			continue // Don't mark as processed, websocket not healthy
		}

		// Mark the error as processed
		err = db.Table("whatsmeow_websocket_errors").
			Where("id = ?", wsError.ID).
			Update("processed", true).Error
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "WebsocketError Update Failed",
				Message: "Failed to mark websocket error as processed: " + err.Error(),
				Entity:  "system",
				Type:    "error",
				Ip:      "system",
			})
		} else {
			log.CreateLog(&entities.Log{
				Title:   "WebsocketError Processed Successfully",
				Message: fmt.Sprintf("Successfully processed websocket error for JID: %s. Reconnected client, restored %d subscriptions, and validated presence events are working.", wsError.ClientJID, len(subscriptions)),
				Entity:  "system",
				Type:    "info",
				Ip:      "system",
			})
		}
	}
}

// CheckWebsocketHealth checks the health of all active websocket connections from client map
func CheckWebsocketHealth() {
	fmt.Println("running websocket health check...")
	var ctx = context.Background()
	db := database.DBClient()
	w := wrapper.WpClient()

	// Get all clients from the active client map
	clientMap := w.GetActiveClients()
	if len(clientMap) == 0 {
		log.CreateLog(&entities.Log{
			Title:   "WebsocketHealth Check",
			Message: "No active clients found in client map",
			Entity:  "system",
			Type:    "info",
			Ip:      "system",
		})
		return
	}

	log.CreateLog(&entities.Log{
		Title:   "WebsocketHealth Check",
		Message: fmt.Sprintf("Checking websocket health for %d active clients", len(clientMap)),
		Entity:  "system",
		Type:    "info",
		Ip:      "system",
	})

	unhealthyCount := 0
	for jid, client := range clientMap {
		// Check if client is connected
		if !client.IsConnected() {
			log.CreateLog(&entities.Log{
				Title:   "WebsocketHealth Not Connected",
				Message: fmt.Sprintf("Client not connected for JID: %s", jid),
				Entity:  "system",
				Type:    "warning",
				Ip:      "system",
			})
			unhealthyCount++

			// Get session info for reconnection
			var session entities.Session
			if err := db.Model(&entities.Session{}).Where("jid = ?", jid).First(&session).Error; err != nil {
				log.CreateLog(&entities.Log{
					Title:   "WebsocketHealth Reconnect Failed",
					Message: fmt.Sprintf("Session not found for JID: %s", jid),
					Entity:  "system",
					Type:    "error",
					Ip:      "system",
				})
				continue
			}

			// Use CheckDevice to reconnect
			log.CreateLog(&entities.Log{
				Title:   "WebsocketHealth Reconnecting",
				Message: fmt.Sprintf("Reconnecting disconnected client for JID: %s", jid),
				Entity:  "system",
				Type:    "info",
				Ip:      "system",
			})

			_, isConnected := w.CheckDevice(ctx, jid, session.RegID)
			if !isConnected {
				log.CreateLog(&entities.Log{
					Title:   "WebsocketHealth Reconnect Failed",
					Message: fmt.Sprintf("Failed to reconnect client for JID: %s", jid),
					Entity:  "system",
					Type:    "error",
					Ip:      "system",
				})
			}
			continue
		}

		// Check websocket health using whatsmeow's built-in function
		isHealthy, err := client.CheckWebSocketHealth()
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "WebsocketHealth Check Failed",
				Message: fmt.Sprintf("Health check error for JID: %s, error: %v", jid, err),
				Entity:  "system",
				Type:    "warning",
				Ip:      "system",
			})
			unhealthyCount++

			// Get session info for reconnection
			var session entities.Session
			if err := db.Model(&entities.Session{}).Where("jid = ?", jid).First(&session).Error; err != nil {
				log.CreateLog(&entities.Log{
					Title:   "WebsocketHealth Reconnect Failed",
					Message: fmt.Sprintf("Session not found for JID: %s", jid),
					Entity:  "system",
					Type:    "error",
					Ip:      "system",
				})
				continue
			}

			// Use CheckDevice to reconnect unhealthy websocket
			log.CreateLog(&entities.Log{
				Title:   "WebsocketHealth Reconnecting",
				Message: fmt.Sprintf("Reconnecting unhealthy websocket for JID: %s", jid),
				Entity:  "system",
				Type:    "info",
				Ip:      "system",
			})

			_, isConnected := w.CheckDevice(ctx, jid, session.RegID)
			if !isConnected {
				log.CreateLog(&entities.Log{
					Title:   "WebsocketHealth Reconnect Failed",
					Message: fmt.Sprintf("Failed to reconnect unhealthy client for JID: %s", jid),
					Entity:  "system",
					Type:    "error",
					Ip:      "system",
				})
			}
			continue
		}

		if !isHealthy {
			log.CreateLog(&entities.Log{
				Title:   "WebsocketHealth Unhealthy",
				Message: fmt.Sprintf("Websocket unhealthy for JID: %s, reconnecting", jid),
				Entity:  "system",
				Type:    "warning",
				Ip:      "system",
			})
			unhealthyCount++

			// Get session info for reconnection
			var session entities.Session
			if err := db.Model(&entities.Session{}).Where("jid = ?", jid).First(&session).Error; err != nil {
				log.CreateLog(&entities.Log{
					Title:   "WebsocketHealth Reconnect Failed",
					Message: fmt.Sprintf("Session not found for JID: %s", jid),
					Entity:  "system",
					Type:    "error",
					Ip:      "system",
				})
				continue
			}

			// Use CheckDevice to reconnect unhealthy websocket
			log.CreateLog(&entities.Log{
				Title:   "WebsocketHealth Reconnecting",
				Message: fmt.Sprintf("Reconnecting unhealthy websocket for JID: %s", jid),
				Entity:  "system",
				Type:    "info",
				Ip:      "system",
			})

			_, isConnected := w.CheckDevice(ctx, jid, session.RegID)
			if !isConnected {
				log.CreateLog(&entities.Log{
					Title:   "WebsocketHealth Reconnect Failed",
					Message: fmt.Sprintf("Failed to reconnect unhealthy client for JID: %s", jid),
					Entity:  "system",
					Type:    "error",
					Ip:      "system",
				})
			}
			continue
		}

		// Websocket is healthy
		log.CreateLog(&entities.Log{
			Title:   "WebsocketHealth Healthy",
			Message: fmt.Sprintf("Websocket healthy for JID: %s", jid),
			Entity:  "system",
			Type:    "info",
			Ip:      "system",
		})
	}

	// Summary log
	healthyCount := len(clientMap) - unhealthyCount
	log.CreateLog(&entities.Log{
		Title:   "WebsocketHealth Check Complete",
		Message: fmt.Sprintf("Health check complete: %d healthy, %d unhealthy out of %d total clients", healthyCount, unhealthyCount, len(clientMap)),
		Entity:  "system",
		Type:    "info",
		Ip:      "system",
	})
}

// StartWebsocketHealthMonitor starts a monitor that checks websocket health every 10 seconds
func StartWebsocketHealthMonitor() {
	fmt.Println("Starting WebsocketHealth monitor - will run every 10 seconds...")

	// Create a ticker that fires every 10 seconds
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	// Run immediately once
	go CheckWebsocketHealth()

	// Then run every 10 seconds
	go func() {
		for {
			select {
			case <-ticker.C:
				CheckWebsocketHealth()
			}
		}
	}()
}
