package cmd

import (
	"context"
	"fmt"
	"time"

	"github.com/sayeworldevelopment/wp-core/pkg/config"
	"github.com/sayeworldevelopment/wp-core/pkg/database"
	"github.com/sayeworldevelopment/wp-core/pkg/entities"
	"github.com/sayeworldevelopment/wp-core/pkg/log"
	"github.com/sayeworldevelopment/wp-core/pkg/server"
	"github.com/sayeworldevelopment/wp-core/pkg/wrapper"
	"go.mau.fi/whatsmeow/types"
)

func StartApp() {
	config := config.InitConfig()
	database.InitDB(config.Database)
	wrapper.Init(config.Database)

	SetDevice()

	// Start cron job to run SetDevice every 3 minutes
	go StartSetDeviceCron()

	fmt.Println("Starting app..., port:", config.App.Port)
	server.LaunchHttpServer(config.App, config.Allows)
}

func SetDevice() {
	fmt.Println("init devices")
	type Device struct {
		JID   string `gorm:"column:jid"`
		RegId string `gorm:"column:registration_id"`
	}
	var devices []Device
	var ctx = context.Background()

	db := database.DBClient()
	w := wrapper.WpClient()

	db.Select("jid,registration_id").Table("whatsmeow_device").Find(&devices)
	for _, v := range devices {
		// Use wrapper's CheckDevice instead of nat's
		client, isConnected := w.CheckDevice(ctx, v.JID, v.RegId)
		if !isConnected {
			log.CreateLog(&entities.Log{
				Title:   "Check Device Failed for Restart",
				Message: "Check Device Failed, check device err",
				Entity:  "user",
				Type:    "error",
				Ip:      "",
			})
			continue
		}

		var session entities.Session
		if err := db.Model(&entities.Session{}).
			Where("jid = ?", v.JID).
			First(&session).Error; err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Check Device Failed for Restart",
				Message: "Check Device Failed, session not found",
				Entity:  "user",
				Type:    "error",
				Ip:      "",
			})

			continue
		}

		// Get all active subscriptions for this session
		var subscriptions []entities.SessionSubscription
		err := db.Where("session_id = ? AND active = ?", session.ID, true).Find(&subscriptions).Error
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Check Device Failed for Restart",
				Message: "Check Device Failed, get subscriptions err:" + err.Error(),
				Entity:  "user",
				Type:    "error",
				Ip:      "",
			})

			continue
		}

		// Subscribe to each phone number
		for _, sub := range subscriptions {
			// Remove existing event handler if it exists
			if sub.EventHandlerId != 0 {
				success := client.RemoveEventHandler(sub.EventHandlerId)
				if success {
					log.CreateLog(&entities.Log{
						Title:   "Event Handler Removed",
						Message: fmt.Sprintf("Successfully removed event handler ID: %d for phone: %s", sub.EventHandlerId, sub.Phone),
						Entity:  "system",
						Type:    "info",
						Ip:      "",
					})
				} else {
					log.CreateLog(&entities.Log{
						Title:   "Event Handler Remove Failed",
						Message: fmt.Sprintf("Failed to remove event handler ID: %d for phone: %s", sub.EventHandlerId, sub.Phone),
						Entity:  "system",
						Type:    "warning",
						Ip:      "",
					})
				}
			}

			// Add new event handler
			newEventHandlerId := client.AddEventHandler(wrapper.EventHandler)

			// Update the EventHandlerId in the database
			err = db.Model(&entities.SessionSubscription{}).
				Where("id = ?", sub.ID).
				Update("event_handler_id", newEventHandlerId).Error
			if err != nil {
				log.CreateLog(&entities.Log{
					Title:   "Update Event Handler ID Failed",
					Message: "Failed to update event handler ID in database: " + err.Error(),
					Entity:  "system",
					Type:    "error",
					Ip:      "",
				})
				continue
			}

			subJid := sub.Phone + "@s.whatsapp.net"
			parsedJID, _ := types.ParseJID(subJid)
			client.SendPresence(types.PresenceAvailable)

			err = client.SubscribePresence(parsedJID)
			if err != nil {
				log.CreateLog(&entities.Log{
					Title:   "Check Device Failed for Restart",
					Message: "Check Device Failed, subscribe presence err:" + err.Error(),
					Entity:  "user",
					Type:    "error",
					Ip:      "",
				})
				continue
			}
			//client.SendPresence(types.PresenceAvailable)

			log.CreateLog(&entities.Log{
				Title:   "Subscription Renewed",
				Message: fmt.Sprintf("Successfully renewed subscription for phone: %s with new event handler ID: %d", sub.Phone, newEventHandlerId),
				Entity:  "system",
				Type:    "info",
				Ip:      "",
			})
		}

	}
}

// StartSetDeviceCron starts a cron job that runs SetDevice every 3 minutes
func StartSetDeviceCron() {
	fmt.Println("Starting SetDevice cron job - will run every 3 minutes...")

	// Create a ticker that fires every 3 minutes
	ticker := time.NewTicker(3 * time.Minute)
	defer ticker.Stop()

	// Log the cron job start
	log.CreateLog(&entities.Log{
		Title:   "SetDevice Cron Started",
		Message: "SetDevice cron job started - will run every 3 minutes",
		Entity:  "system",
		Type:    "info",
		Ip:      "system",
	})

	// Run the cron job
	for range ticker.C {
		fmt.Println("Running scheduled SetDevice...")

		// Log the scheduled run
		log.CreateLog(&entities.Log{
			Title:   "SetDevice Scheduled Run",
			Message: "Running SetDevice as scheduled (every 3 minutes)",
			Entity:  "system",
			Type:    "info",
			Ip:      "system",
		})

		// Run SetDevice
		SetDevice()
	}
}
