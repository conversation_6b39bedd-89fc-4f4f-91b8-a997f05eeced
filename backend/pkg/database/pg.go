package database

import (
	"fmt"
	"log"
	"os"
	"sync"

	"github.com/sayeworldevelopment/wp-core/pkg/config"
	"github.com/sayeworldevelopment/wp-core/pkg/entities"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

var (
	db          *gorm.DB
	err         error
	client_once sync.Once
)

func InitDB(dbc config.Database) {
	client_once.Do(func() {
		dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable TimeZone=Europe/Istanbul", dbc.Host, dbc.Port, dbc.User, dbc.Pass, dbc.Name)
		db, err = gorm.Open(
			postgres.New(
				postgres.Config{
					DSN:                  dsn,
					PreferSimpleProtocol: true,
				},
			),
		)
		if err != nil {
			panic(err)
		}
		err := db.AutoMigrate(
			&entities.Log{},
			&entities.Version{},
			&entities.Device{},
			&entities.Message{},
			&entities.Presence{},
			&entities.Session{},
			&entities.SessionEvent{},
			&entities.SessionSubscription{},
			&entities.WebsocketError{},
			&entities.LoginCode{},
			&entities.Proxy{},
		)
		if err != nil {
			log.Println("AutoMigrate error:", err.Error())
		}

		if os.Getenv("DEV_MODE") == "true" {
			InitSeed()
		}
	})
}

func DBClient() *gorm.DB {
	if db == nil {
		log.Panic("Postgres is not initialized. Call InitDB first.")
	}
	return db
}
