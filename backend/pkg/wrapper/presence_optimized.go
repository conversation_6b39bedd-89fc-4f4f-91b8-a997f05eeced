// Copyright (c) 2021 Tulir Asokan
//
// This Source Code Form is subject to the terms of the Mozilla Public
// License, v. 2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/.

package wrapper

import (
	"context"
	"time"

	"github.com/gorilla/websocket"
	"go.mau.fi/whatsmeow"
	"go.mau.fi/whatsmeow/types"
	"go.mau.fi/whatsmeow/types/events"
)

// PresenceOptimizedConfig contains configuration for presence-optimized websocket connections
type PresenceOptimizedConfig struct {
	// Enable presence-optimized mode
	Enabled bool

	// Reduce keepalive frequency for presence-only usage
	KeepAliveInterval time.Duration

	// Faster reconnection for presence
	ReconnectDelay time.Duration

	// Ignore 1006 errors in logs
	IgnoreAbnormalClosureErrors bool

	// Custom websocket dialer settings
	HandshakeTimeout time.Duration
	ReadBufferSize   int
	WriteBufferSize  int
}

// DefaultPresenceOptimizedConfig returns default configuration for presence-optimized connections
func DefaultPresenceOptimizedConfig() *PresenceOptimizedConfig {
	return &PresenceOptimizedConfig{
		Enabled:                     true,
		KeepAliveInterval:           45 * time.Second, // Longer interval for presence-only
		ReconnectDelay:              5 * time.Second,  // Faster reconnect
		IgnoreAbnormalClosureErrors: true,
		HandshakeTimeout:            30 * time.Second,
		ReadBufferSize:              4096,
		WriteBufferSize:             4096,
	}
}

// SetupPresenceOptimizedClient configures the client for presence-optimized usage
func SetupPresenceOptimizedClient(client *whatsmeow.Client, config *PresenceOptimizedConfig) {
	if config == nil {
		config = DefaultPresenceOptimizedConfig()
	}

	if config.Enabled {
		// Enable auto-reconnect with faster timing
		client.EnableAutoReconnect = true

		// Send presence available to ensure we're online
		client.SendPresence(types.PresenceAvailable)
	}
}

// ConnectForPresence connects the client optimized for presence usage
func ConnectForPresence(client *whatsmeow.Client, config *PresenceOptimizedConfig) error {
	SetupPresenceOptimizedClient(client, config)
	return client.Connect()
}

// SubscribePresenceOptimized subscribes to presence with optimized error handling
func SubscribePresenceOptimized(client *whatsmeow.Client, jid types.JID) error {
	// First ensure we're online for presence
	if err := client.SendPresence(types.PresenceAvailable); err != nil {
		// Log warning but don't fail - presence might still work
		return err
	}

	// Subscribe to presence with retry logic
	var err error
	for retry := 0; retry < 3; retry++ {
		err = client.SubscribePresence(jid)
		if err == nil {
			break
		}
		
		// Wait before retry
		time.Sleep(time.Duration(retry+1) * time.Second)
	}

	if err != nil {
		return err
	}

	// Send presence available again after subscription
	client.SendPresence(types.PresenceAvailable)
	return nil
}

// HandlePresenceEvents sets up optimized presence event handling
func HandlePresenceEvents(client *whatsmeow.Client, handler func(*events.Presence)) uint32 {
	return client.AddEventHandler(func(evt any) {
		if presence, ok := evt.(*events.Presence); ok {
			handler(presence)
		}
	})
}

// MonitorPresenceConnection monitors connection health for presence usage
func MonitorPresenceConnection(ctx context.Context, client *whatsmeow.Client) {
	ticker := time.NewTicker(30 * time.Second) // Check every 30 seconds
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			// First check basic connection
			if !client.IsConnected() {
				continue // Auto-reconnect should handle this
			}

			// Send presence to keep connection alive
			client.SendPresence(types.PresenceAvailable)
		}
	}
}

// ValidatePresenceSubscription checks if presence subscription is working
func ValidatePresenceSubscription(client *whatsmeow.Client, jid types.JID) bool {
	if !client.IsConnected() {
		return false
	}

	// Try to send presence and subscribe again
	client.SendPresence(types.PresenceAvailable)
	err := client.SubscribePresence(jid)
	return err == nil
}
