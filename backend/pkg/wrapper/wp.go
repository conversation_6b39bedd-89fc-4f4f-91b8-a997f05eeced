package wrapper

import (
	"context"
	"errors"
	"fmt"
	"image"
	"image/color"
	"strconv"
	"strings"
	"time"

	"github.com/makiuchi-d/gozxing"
	"github.com/sayeworldevelopment/wp-core/pkg/config"
	"github.com/sayeworldevelopment/wp-core/pkg/consts"
	"github.com/sayeworldevelopment/wp-core/pkg/database"
	"github.com/sayeworldevelopment/wp-core/pkg/dtos"
	"github.com/sayeworldevelopment/wp-core/pkg/entities"
	sayelog "github.com/sayeworldevelopment/wp-core/pkg/log"
	"github.com/sayeworldevelopment/wp-core/pkg/proxy"
	"github.com/sayeworldevelopment/wp-core/pkg/utils"

	"github.com/labstack/gommon/log"
	_ "github.com/lib/pq"

	_log "log"

	"go.mau.fi/whatsmeow"
	"go.mau.fi/whatsmeow/store/sqlstore"
	"go.mau.fi/whatsmeow/types"
	"go.mau.fi/whatsmeow/types/events"
	waLog "go.mau.fi/whatsmeow/util/log"
)

type Client struct {
	MContainer *sqlstore.Container
	// devices    []*store.Device
	// activeClients stores active WhatsApp clients by JID
	activeClients map[string]*whatsmeow.Client
}

var WPW *Client
var GenerateImageOnLogin = false

func Init(db config.Database) {
	ctx := context.Background()
	fmt.Println("wrapper init...")
	url := fmt.Sprintf("postgres://%s:%s@%s:%s/%s?sslmode=disable", db.User, db.Pass, db.Host, db.Port, db.Name)
	container, err := sqlstore.New(ctx, "postgres", url, nil)
	if err != nil {
		fmt.Println("wrapper init error:", err.Error())
		panic(err)
	}
	log.Info("wrapper init successfully...")
	WPW = &Client{
		MContainer:    container,
		activeClients: make(map[string]*whatsmeow.Client),
	}
}

func (w *Client) GetCode(ctx context.Context, req dtos.GetCodeReq) (dtos.GetCodeResp, error) {
	var (
		resp dtos.GetCodeResp
	)
	//find session
	var session entities.Session
	db := database.DBClient()
	if err := db.Model(&entities.Session{}).Where("id = ?", req.SessionID).First(&session).Error; err != nil {
		return resp, errors.New("session not found for id: " + req.SessionID)
	}

	device := w.MContainer.NewDevice()
	clientLog := waLog.Stdout("Client", "DEBUG", true)

	client := whatsmeow.NewClient(device, clientLog)

	if session.ProxyUsed {
		s_proxy, err := proxy.SetUrlForSession(ctx, session.ID)
		if err != nil {
			return resp, errors.New("proxy error: " + err.Error())
		}
		client.SetProxy(s_proxy)
	}

	client.EnableAutoReconnect = true
	if err := client.Connect(); err != nil {
		return resp, errors.New("client connect error: " + err.Error())
	}

	regId := strconv.FormatUint(uint64(device.RegistrationID), 10)
	code, err := client.PairPhone(ctx, req.Phone, true, whatsmeow.PairClientOtherWebClient, "Chrome (Windows)")
	if err != nil {
		return resp, errors.New("code isn't generate,err: " + err.Error())
	}

	resp.Code = code
	resp.RegId = string(regId)

	loginCode := entities.LoginCode{
		Code:           code,
		PhoneNumber:    req.Phone,
		RegistrationID: string(regId),
		Status:         1,
		IP:             req.IP,
		SessionID:      req.SessionID,
	}

	err = db.Create(&loginCode).Error
	if err != nil {
		return resp, err
	}

	return resp, nil
}

func (w *Client) GetQr(ctx context.Context, req dtos.GetCodeReq) (string, string, error) {
	var err error
	device := w.MContainer.NewDevice()
	regId := strconv.FormatUint(uint64(device.RegistrationID), 10)
	clientLog := waLog.Stdout("Client", "DEBUG", true)
	client := whatsmeow.NewClient(device, clientLog)
	s_proxy, err := proxy.SetUrlWithoutSession(ctx, req.IP, req.Phone)
	if err != nil {
		return "", string(regId), errors.New("proxy error: " + err.Error())
	}
	client.SetProxy(s_proxy)
	qrChan, err := client.GetQRChannel(context.Background())
	if err != nil {
		return "", string(regId), err
	}

	client.EnableAutoReconnect = true
	err = client.Connect()
	if err != nil {
		return "", string(regId), err
	}
	for evt := range qrChan {
		if evt.Event == "code" {
			return evt.Code, string(regId), err
		} else {
			fmt.Print("Bağlantı bulunamadı2")
		}
	}
	return "", "", nil

}

func (w *Client) CheckDevice(ctx context.Context, jid string, regId string) (*whatsmeow.Client, bool) {

	// Check if there's an existing client for this JID and close it
	if existingClient, exists := w.activeClients[jid]; exists {
		fmt.Println("Found existing client for JID:", jid, "- disconnecting...")
		existingClient.Disconnect()
		delete(w.activeClients, jid)
		fmt.Println("Existing client disconnected and removed for JID:", jid)
	}

	senderArr := strings.Split(jid, "@")
	sender := types.NewJID(senderArr[0], types.DefaultUserServer)
	device, err := w.MContainer.GetDevice(ctx, sender)
	if err != nil {
		return nil, false
	}
	if device == nil {
		return nil, false
	}

	// find session
	var session entities.Session
	db := database.DBClient()
	if err := db.Model(&entities.Session{}).
		Where("reg_id = ?", regId).
		First(&session).Error; err != nil {
		return nil, false
	}

	clientLog := waLog.Stdout("Client", "ERROR", true)
	client := whatsmeow.NewClient(device, clientLog)

	if session.ProxyUsed {
		s_proxy, err := proxy.SetUrlForSession(ctx, session.ID)
		if err != nil {
			return nil, false
		}
		client.SetProxy(s_proxy)
	}
	// Setup presence-optimized client
	SetupPresenceOptimizedClient(client, DefaultPresenceOptimizedConfig())

	client.AddEventHandler(EventHandler)

	err = ConnectForPresence(client, DefaultPresenceOptimizedConfig())
	if err != nil {
		return nil, false
	}

	// Store the client in our active clients map
	w.activeClients[jid] = client
	client.SendPresence(types.PresenceAvailable)
	session.JID = jid
	err = db.Updates(&session).Error
	if err != nil {
		_log.Println("Error updating session:", err.Error())
		return nil, false
	}
	return client, true
}

func (w *Client) GetProfilePhoto(ctx context.Context, jid string) (string, error) {

	pjid, _ := types.ParseJID(jid)
	device, err := w.MContainer.GetDevice(ctx, pjid)
	if err != nil {
		return "", err
	}
	if device == nil {
		return "", errors.New("device not found")
	}

	// find session
	var session entities.Session
	db := database.DBClient()
	if err := db.Model(&entities.Session{}).
		Where("jid = ?", jid).
		First(&session).Error; err != nil {
		return "", err
	}

	_log.Println("session name: ", session.ProxyID)

	clientLog := waLog.Stdout("Client", "ERROR", true)
	client := whatsmeow.NewClient(device, clientLog)

	if session.ProxyUsed {
		s_proxy, err := proxy.SetUrlForSession(ctx, session.ID)
		if err != nil {
			return "", errors.New("proxy error: " + err.Error())
		}
		client.SetProxy(s_proxy)
	}
	client.EnableAutoReconnect = true
	err = client.Connect()
	if err != nil {
		return "", err
	}

	picture, err := client.GetProfilePictureInfo(pjid.ToNonAD(), &whatsmeow.GetProfilePictureParams{
		Preview:     false,
		ExistingID:  "",
		IsCommunity: false,
	})

	if err != nil {
		return "", err
	}
	return picture.URL, nil
}

// GetProfilePhotoByPhone gets profile photo for a specific phone number using a device
func (w *Client) GetProfilePhotoByPhone(ctx context.Context, jid string, phoneNumber string) (string, error) {
	pjid, _ := types.ParseJID(jid)
	device, err := w.MContainer.GetDevice(ctx, pjid)
	if err != nil {
		return "", err
	}
	if device == nil {
		return "", errors.New("device not found")
	}

	// find session
	var session entities.Session
	db := database.DBClient()
	if err := db.Model(&entities.Session{}).
		Where("jid = ?", jid).
		First(&session).Error; err != nil {
		return "", err
	}

	_log.Println("session name: ", session.ProxyID)

	clientLog := waLog.Stdout("Client", "ERROR", true)
	client := whatsmeow.NewClient(device, clientLog)

	if session.ProxyUsed {
		s_proxy, err := proxy.SetUrlForSession(ctx, session.ID)
		if err != nil {
			return "", errors.New("proxy error: " + err.Error())
		}
		client.SetProxy(s_proxy)
	}

	client.EnableAutoReconnect = true
	err = client.Connect()
	if err != nil {
		return "", err
	}

	// Parse the phone number to JID
	targetJID, err := types.ParseJID(phoneNumber + "@s.whatsapp.net")
	if err != nil {
		return "", errors.New("invalid phone number format")
	}

	picture, err := client.GetProfilePictureInfo(targetJID, &whatsmeow.GetProfilePictureParams{
		Preview:     false,
		ExistingID:  "",
		IsCommunity: false,
	})

	if err != nil {
		return "", err
	}
	return picture.URL, nil
}

func (w *Client) SubscribePresence(ctx context.Context, jid, subscribePhone string) (uint32, error) {

	pjid, _ := types.ParseJID(jid)

	device, err := w.MContainer.GetDevice(ctx, pjid)
	if err != nil {
		return 0, err
	}
	if device == nil {
		return 0, errors.New("device not found")
	}

	fmt.Println("device id", device.RegistrationID)

	// find session
	var session entities.Session
	db := database.DBClient()
	if err := db.Model(&entities.Session{}).
		Where("jid = ?", jid).
		First(&session).Error; err != nil {
		return 0, err
	}

	subJid := subscribePhone + "@s.whatsapp.net"

	// Check if we have an active client for this JID
	if existingClient, exists := w.activeClients[jid]; exists && existingClient.IsConnected() {
		fmt.Println("Using existing client for subscription:", subscribePhone)

		var existingSubscribe entities.SessionSubscription
		err = db.Where("session_id = ? AND phone = ?", session.ID, subscribePhone).First(&existingSubscribe).Error
		if err == nil {
			return existingSubscribe.EventHandlerId, nil
		}

		parsedJID, _ := types.ParseJID(subJid)
		existingClient.SendPresence(types.PresenceAvailable)
		//eventHandler add
		eventHandlerId := existingClient.AddEventHandler(EventHandler)

		err = existingClient.SubscribePresence(parsedJID)
		if err != nil {
			fmt.Println("SubscribePresence error with existing client:", err.Error())
			return 0, err
		}

		//existingClient.SendPresence(types.PresenceAvailable)

		var firstSubscribe entities.SessionSubscription
		db.Where("session_id = ?", session.ID).First(&firstSubscribe)

		subscribe := entities.SessionSubscription{
			SessionID:      session.ID,
			Phone:          subscribePhone,
			JID:            subJid,
			Active:         true,
			EventHandlerId: firstSubscribe.EventHandlerId,
		}
		err = db.Create(&subscribe).Error
		if err != nil {
			sayelog.CreateLog(&entities.Log{
				Title:   "Subscribe Presence Failed",
				Message: "Subscribe Presence Failed, create subscription err:" + err.Error(),
				Entity:  "user",
				Type:    "error",
				Ip:      "unknown",
			})
			return 0, err
		}

		return eventHandlerId, nil
	}

	clientLog := waLog.Stdout("Client", "ERROR", true)
	client := whatsmeow.NewClient(device, clientLog)
	if session.ProxyUsed {
		s_proxy, err := proxy.SetUrlForSession(ctx, session.ID)
		if err != nil {
			return 0, errors.New("proxy error: " + err.Error())
		}
		client.SetProxy(s_proxy)
	}

	client.SendPresence(types.PresenceAvailable)

	eventHandlerId := client.AddEventHandler(EventHandler)
	client.EnableAutoReconnect = true
	err = client.Connect()
	if err != nil {
		return eventHandlerId, err
	}

	// Store the client in our active clients map
	w.activeClients[jid] = client

	client.SendPresence(types.PresenceAvailable)

	fmt.Println("SubscribePresence subJid:", subJid)
	parsedJID, _ := types.ParseJID(subJid)

	fmt.Println("SubscribePresence parsedJID:", parsedJID)
	err = client.SubscribePresence(parsedJID)
	if err != nil {
		fmt.Println("SubscribePresence error:", err.Error())
		return eventHandlerId, err
	}
	client.SendPresence(types.PresenceAvailable)

	subscribe := entities.SessionSubscription{
		SessionID:      session.ID,
		Phone:          subscribePhone,
		JID:            subJid,
		Active:         true,
		EventHandlerId: eventHandlerId,
	}
	err = db.Create(&subscribe).Error
	if err != nil {
		sayelog.CreateLog(&entities.Log{
			Title:   "Subscribe Presence Failed",
			Message: "Subscribe Presence Failed, create subscription err:" + err.Error(),
			Entity:  "user",
			Type:    "error",
			Ip:      "unknown",
		})
		return eventHandlerId, err
	}

	return eventHandlerId, nil
}

func (w *Client) GetPresence(ctx context.Context) {
}

func (w *Client) RemovePresenceSubscription(ctx context.Context, jid, subscribePhone string, eventHandlerID uint32) error {
	pjid, _ := types.ParseJID(jid)
	device, err := w.MContainer.GetDevice(ctx, pjid)
	if err != nil {
		return err
	}
	if device == nil {
		return errors.New("device not found")
	}

	// find session
	var session entities.Session
	db := database.DBClient()
	if err := db.Model(&entities.Session{}).
		Where("jid = ?", jid).
		First(&session).Error; err != nil {
		return err
	}

	// Check if we have an active client for this JID
	if existingClient, exists := w.activeClients[jid]; exists {
		// Check if this was the last subscription for this session
		var remainingSubscriptions []entities.SessionSubscription
		db.Where("session_id = ? AND phone != ?", session.ID, subscribePhone).Find(&remainingSubscriptions)

		// If no more subscriptions for this session, remove event handler and disconnect client
		if len(remainingSubscriptions) == 0 {
			success := existingClient.RemoveEventHandler(eventHandlerID)
			if success {
				fmt.Println("Successfully removed event handler ID:", eventHandlerID)
			} else {
				fmt.Println("Failed to remove event handler ID:", eventHandlerID)
			}
			existingClient.Disconnect()
			delete(w.activeClients, jid)
			fmt.Println("Disconnected and removed client for JID:", jid)
		} else {
			fmt.Println("Keeping client active, remaining subscriptions:", len(remainingSubscriptions))
		}
	}

	// Remove from database
	err = db.Where("session_id = ? AND phone = ?", session.ID, subscribePhone).Delete(&entities.SessionSubscription{}).Error
	if err != nil {
		fmt.Println("Database delete error:", err.Error())
		return err
	}

	fmt.Println("Successfully removed presence subscription for:", subscribePhone)
	return nil
}

func BitMatrixToImage(bitMatrix *gozxing.BitMatrix) *image.Gray {
	width := bitMatrix.GetWidth()
	height := bitMatrix.GetHeight()
	img := image.NewGray(image.Rect(0, 0, width, height))
	for x := 0; x < width; x++ {
		for y := 0; y < height; y++ {
			if bitMatrix.Get(x, y) {
				img.Set(x, y, color.Black)
			} else {
				img.Set(x, y, color.White)
			}
		}
	}
	return img
}

/*

func (w *Client) GetPresence(ctx context.Context, jid string) (string, error) {

	pjid, _ := types.ParseJID(jid)
	device, err := w.MContainer.GetDevice(pjid)
	if err != nil {
		return "", err
	}
	if device == nil {
		return "", errors.New("device not found")
	}

	clientLog := waLog.Stdout("Client", "ERROR", true)
	client := whatsmeow.NewClient(device, clientLog)
	err = client.Connect()
	if err != nil {
		return "", err
	}

	picture, err := client.SubscribePresence(pjid.ToNonAD(), &whatsmeow.GetProfilePictureParams{
		Preview:     false,
		ExistingID:  "",
		IsCommunity: false,
	})

	if err != nil {
		return "", err
	}
	return picture.URL, nil
}*/

// CleanupClientForJID removes and disconnects a client for a specific JID
func (w *Client) CleanupClientForJID(jid string) {
	if client, exists := w.activeClients[jid]; exists {
		client.Disconnect()
		delete(w.activeClients, jid)
		fmt.Println("Cleaned up client for JID:", jid)
	}
}

// CleanupAllClients disconnects and removes all active clients
func (w *Client) CleanupAllClients() {
	for jid, client := range w.activeClients {
		client.Disconnect()
		delete(w.activeClients, jid)
		fmt.Println("Cleaned up client for JID:", jid)
	}
}

func WpClient() *Client {
	return WPW
}

func EventHandler(evt interface{}) {
	switch v := evt.(type) {
	case *events.Presence:

		lg := utils.ToJSONString(v)
		sayelog.CreateLog(&entities.Log{
			Title:   "Presence Event:" + lg,
			Message: "Presence event running",
			Entity:  "user",
			Type:    "info",
			Ip:      "unknown",
		})

		var subscribes []entities.SessionSubscription
		db := database.DBClient()

		phone, _ := ParseNumberFromJID(v.From.String())

		db.Where("phone = ?", phone).Debug().Find(&subscribes)
		status := consts.PresenceOnline
		if v.Unavailable {
			status = consts.PresenceOffline
		}

		sayelog.CreateLog(&entities.Log{
			Title:   "Presence Event Subscribes:" + utils.ToJSONString(subscribes),
			Message: "Presence event running for subscribe",
			Entity:  "user",
			Type:    "info",
			Ip:      "unknown",
		})

		for _, sub := range subscribes {
			var presence, lastPresence entities.Presence
			presence.SessionId = sub.SessionID.String()
			presence.Status = status
			if status == consts.PresenceOffline {
				presence.LastSeen = time.Now()
			}
			presence.SubscribePhone = sub.Phone

			db.Where("subscribe_phone = ? and session_id = ?", sub.Phone, sub.SessionID).Debug().Order("created_at desc").First(&lastPresence)
			if lastPresence.Status == status {
				continue
			}
			err := db.Create(&presence).Error
			if err != nil {
				sayelog.CreateLog(&entities.Log{
					Title:   "Presence Event Create Error:" + err.Error(),
					Message: "Presence event create error",
					Entity:  "user",
					Type:    "error",
					Ip:      "unknown",
				})
			}
			sayelog.CreateLog(&entities.Log{
				Title:   "Presence Event Created:" + utils.ToJSONString(presence),
				Message: "Presence event created",
				Entity:  "user",
				Type:    "info",
				Ip:      "unknown",
			})
		}

		fmt.Println("Presence event running", v.LastSeen)
		jid := v.From.String()

		fmt.Println("Presence From", jid)
		fmt.Println("Presence Last Seen", v.LastSeen)
		fmt.Println("Presence Available", status)

		//case *events.Message:
		//	fmt.Println("Message event running", v.Message)
		//	fmt.Println("Message From", v.Info.Chat.User)
	}
}

func ParseNumberFromJID(jid string) (string, error) {
	parts := strings.Split(jid, "@")
	if len(parts) < 2 {
		return "", fmt.Errorf("geçersiz JID formatı: %s", jid)
	}
	return parts[0], nil
}
